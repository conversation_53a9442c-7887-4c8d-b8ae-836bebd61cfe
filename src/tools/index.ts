import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { executeShellTool } from "./execute-shell/index.js";
import { getSystemInfoTool } from "./get-system-info/index.js";
import { listAllowedCommandsTool } from "./list-allowed-commands/index.js";

// 工具列表
const tools = [
  executeShellTool,
  getSystemInfoTool,
  listAllowedCommandsTool
];

/**
 * 注册所有工具到 MCP 服务器
 */
export function registerTools(server: McpServer) {
  tools.forEach(tool => {
    server.registerTool(
      tool.name,
      tool.config,
      tool.handler
    );
  });

  // 检测是否在 MCP Inspector 环境中运行
  const isInspectorMode = process.env.MCP_INSPECTOR ||
                         process.argv.some(arg => arg.includes('inspector')) ||
                         process.env.npm_lifecycle_event === 'dev:inspector';

  // 只在非 Inspector 环境下显示工具注册信息
  if (!isInspectorMode) {
    console.error(`✅ Registered ${tools.length} tools:`);
    tools.forEach(tool => {
      console.error(`   • ${tool.name}: ${tool.config.description}`);
    });
  }
}

/**
 * 获取所有工具的信息
 */
export function getToolsInfo() {
  return tools.map(tool => ({
    name: tool.name,
    title: tool.config.title,
    description: tool.config.description
  }));
}
