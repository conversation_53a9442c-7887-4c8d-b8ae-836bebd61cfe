import { z } from "zod";
import { exec } from "child_process";
import { promisify } from "util";
const execAsync = promisify(exec);
// 输入参数验证
const ExecuteShellInputSchema = z.object({
    command: z.string().min(1, "Command cannot be empty"),
    timeout: z.number().min(1).max(300).optional().default(20)
});
/**
 * 执行 shell 命令的处理函数
 */
async function executeShellHandler(args) {
    try {
        // 验证输入
        const validatedInput = ExecuteShellInputSchema.parse(args);
        const { command, timeout } = validatedInput;
        // 直接执行命令
        const result = await execAsync(command, {
            timeout: timeout * 1000, // 转换为毫秒
            maxBuffer: 1024 * 1024 * 10, // 10MB buffer
            encoding: 'utf8'
        });
        let output = `Command: ${command}\n\n`;
        if (result.stdout) {
            output += `STDOUT:\n${result.stdout}\n`;
        }
        if (result.stderr) {
            output += `STDERR:\n${result.stderr}\n`;
        }
        if (!result.stdout && !result.stderr) {
            output += `No output produced\n`;
        }
        return {
            content: [{
                    type: "text",
                    text: output
                }]
        };
    }
    catch (error) {
        let errorMessage = `Command: ${args.command}\n\n`;
        errorMessage += `Error: ${error.message || "Unknown error occurred"}\n`;
        if (error.stdout) {
            errorMessage += `STDOUT:\n${error.stdout}\n`;
        }
        if (error.stderr) {
            errorMessage += `STDERR:\n${error.stderr}\n`;
        }
        return {
            content: [{
                    type: "text",
                    text: errorMessage
                }],
            isError: true
        };
    }
}
// 导出工具配置
export const executeShellTool = {
    name: "execute-shell",
    config: {
        title: "Execute Shell Command",
        description: "Execute a shell command directly",
        inputSchema: {
            type: "object",
            properties: {
                command: {
                    type: "string",
                    description: "The shell command to execute"
                },
                timeout: {
                    type: "number",
                    description: "Timeout in seconds (default: 20, max: 300)",
                    default: 20,
                    minimum: 1,
                    maximum: 300
                }
            },
            required: ["command"]
        }
    },
    handler: executeShellHandler
};
//# sourceMappingURL=index.js.map