/**
 * 执行 shell 命令的处理函数
 */
declare function executeShellHandler(args: any): Promise<{
    content: {
        type: "text";
        text: string;
    }[];
    isError?: undefined;
} | {
    content: {
        type: "text";
        text: string;
    }[];
    isError: boolean;
}>;
export declare const executeShellTool: {
    name: string;
    config: {
        title: string;
        description: string;
        inputSchema: {
            type: string;
            properties: {
                command: {
                    type: string;
                    description: string;
                };
                timeout: {
                    type: string;
                    description: string;
                    default: number;
                    minimum: number;
                    maximum: number;
                };
            };
            required: string[];
        };
    };
    handler: typeof executeShellHandler;
};
export {};
//# sourceMappingURL=index.d.ts.map