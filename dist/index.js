#!/usr/bin/env node
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import { registerTools } from "./tools/index.js";
// 创建 MCP 服务器
const server = new McpServer({
    name: "shell-executor",
    version: "1.0.0"
});
// 注册所有工具
registerTools(server);
// 启动服务器
async function main() {
    console.error("🚀 Starting MCP Shell Executor Server...");
    const transport = new StdioServerTransport();
    await server.connect(transport);
    console.error("✅ MCP Shell Executor Server is running!");
    console.error("🔒 Security features enabled");
    console.error("📝 Use 'list-allowed-commands' tool to see security configuration");
}
// 错误处理
process.on('uncaughtException', (error) => {
    console.error('❌ Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// 启动服务器
main().catch((error) => {
    console.error("❌ Failed to start server:", error);
    process.exit(1);
});
//# sourceMappingURL=index.js.map