#!/bin/bash

# MCP Shell Executor Development Server with Hot Reload

echo "🔥 Starting MCP Shell Executor in Development Mode with Hot Reload..."

# Parse command line arguments
LAUNCH_INSPECTOR=false
TYPE_CHECK=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --inspector|-i)
            LAUNCH_INSPECTOR=true
            shift
            ;;
        --type-check|-t)
            TYPE_CHECK=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --inspector, -i        Launch MCP Inspector with hot reload server"
            echo "  --type-check, -t       Enable TypeScript type checking in watch mode"
            echo "  --help, -h             Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                     Start development server with hot reload"
            echo "  $0 --inspector         Start with MCP Inspector"
            echo "  $0 --type-check        Start with type checking"
            echo "  $0 -i -t               Start with inspector and type checking"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check Node.js version and use nvm to switch to v18.3.0
REQUIRED_VERSION="18.3.0"
CURRENT_VERSION=$(node -v 2>/dev/null | cut -d'v' -f2 || echo "0.0.0")

echo "🔍 Current Node.js version: v$CURRENT_VERSION"
echo "🎯 Required Node.js version: v$REQUIRED_VERSION"

if [ "$CURRENT_VERSION" != "$REQUIRED_VERSION" ]; then
    echo "🔄 Switching to Node.js v$REQUIRED_VERSION using nvm..."

    # Check if nvm is available
    if command -v nvm &> /dev/null || [ -s "$HOME/.nvm/nvm.sh" ]; then
        # Load nvm if it's not already loaded
        if [ -s "$HOME/.nvm/nvm.sh" ] && ! command -v nvm &> /dev/null; then
            source "$HOME/.nvm/nvm.sh"
        fi

        # Try to use the specific version
        if nvm use $REQUIRED_VERSION &> /dev/null; then
            NEW_VERSION=$(node -v | cut -d'v' -f2)
            echo "✅ Successfully switched to Node.js v$NEW_VERSION"
        else
            echo "⚠️  Node.js v$REQUIRED_VERSION not found. Installing..."
            if nvm install $REQUIRED_VERSION; then
                nvm use $REQUIRED_VERSION
                NEW_VERSION=$(node -v | cut -d'v' -f2)
                echo "✅ Installed and switched to Node.js v$NEW_VERSION"
            else
                echo "❌ Failed to install Node.js v$REQUIRED_VERSION"
                exit 1
            fi
        fi
    else
        echo "❌ nvm is not available. Please install nvm first:"
        echo "   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
        echo "   Then restart your terminal and run this script again."
        exit 1
    fi
else
    echo "✅ Already using the correct Node.js version: v$CURRENT_VERSION"
fi

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "🛑 Shutting down development server..."
    if [ ! -z "$DEV_PID" ]; then
        kill $DEV_PID 2>/dev/null
    fi
    if [ ! -z "$INSPECTOR_PID" ]; then
        kill $INSPECTOR_PID 2>/dev/null
    fi
    if [ ! -z "$TYPE_CHECK_PID" ]; then
        kill $TYPE_CHECK_PID 2>/dev/null
    fi
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Start type checking in background if requested
if [ "$TYPE_CHECK" = true ]; then
    echo "🔍 Starting TypeScript type checking in watch mode..."
    npm run type-check:watch &
    TYPE_CHECK_PID=$!
fi

if [ "$LAUNCH_INSPECTOR" = true ]; then
    echo "🧪 Starting development server with MCP Inspector..."
    echo "🔥 Hot reload enabled - changes will restart the server automatically"
    echo "💡 Press Ctrl+C to stop"
    echo ""
    
    # Start development server in background
    npm run dev &
    DEV_PID=$!
    
    # Wait a moment for server to start
    sleep 3
    
    # Launch inspector pointing to the tsx command
    echo "🚀 Launching MCP Inspector..."
    npx @modelcontextprotocol/inspector tsx src/index.ts &
    INSPECTOR_PID=$!
    
    # Wait for processes
    wait
    
else
    echo "🔥 Starting development server with hot reload..."
    echo "💡 Server will restart automatically when you modify files"
    echo "💡 Press Ctrl+C to stop"
    echo ""
    
    # Start development server with hot reload
    npm run dev
fi
