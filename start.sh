#!/bin/bash

# MCP Shell Executor Server Startup Script

echo "🚀 Starting MCP Shell Executor Server..."

# Parse command line arguments
LAUNCH_INSPECTOR=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --test|--inspector|-t)
            LAUNCH_INSPECTOR=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --test, --inspector, -t    Launch MCP Inspector for testing"
            echo "  --help, -h                 Show this help message"
            echo ""
            echo "Examples:"
            echo "  $0                         Start server only"
            echo "  $0 --test                  Launch MCP Inspector with server"
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version and use nvm to switch to v18.3.0
REQUIRED_VERSION="18.3.0"
CURRENT_VERSION=$(node -v 2>/dev/null | cut -d'v' -f2 || echo "0.0.0")

echo "🔍 Current Node.js version: v$CURRENT_VERSION"
echo "🎯 Required Node.js version: v$REQUIRED_VERSION"

if [ "$CURRENT_VERSION" != "$REQUIRED_VERSION" ]; then
    echo "🔄 Switching to Node.js v$REQUIRED_VERSION using nvm..."

    # Check if nvm is available
    if command -v nvm &> /dev/null || [ -s "$HOME/.nvm/nvm.sh" ]; then
        # Load nvm if it's not already loaded
        if [ -s "$HOME/.nvm/nvm.sh" ] && ! command -v nvm &> /dev/null; then
            source "$HOME/.nvm/nvm.sh"
        fi

        # Try to use the specific version
        if nvm use $REQUIRED_VERSION &> /dev/null; then
            NEW_VERSION=$(node -v | cut -d'v' -f2)
            echo "✅ Successfully switched to Node.js v$NEW_VERSION"
        else
            echo "⚠️  Node.js v$REQUIRED_VERSION not found. Installing..."
            if nvm install $REQUIRED_VERSION; then
                nvm use $REQUIRED_VERSION
                NEW_VERSION=$(node -v | cut -d'v' -f2)
                echo "✅ Installed and switched to Node.js v$NEW_VERSION"
            else
                echo "❌ Failed to install Node.js v$REQUIRED_VERSION"
                exit 1
            fi
        fi
    else
        echo "❌ nvm is not available. Please install nvm first:"
        echo "   curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash"
        echo "   Then restart your terminal and run this script again."
        exit 1
    fi
else
    echo "✅ Already using the correct Node.js version: v$CURRENT_VERSION"
fi

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "📦 Building project..."
    npm run build
    if [ $? -ne 0 ]; then
        echo "❌ Build failed. Please check the errors above."
        exit 1
    fi
fi

# Function to open browser (cross-platform)
open_browser() {
    local url=$1
    if command -v open &> /dev/null; then
        # macOS
        open "$url"
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open "$url"
    elif command -v start &> /dev/null; then
        # Windows (Git Bash)
        start "$url"
    else
        echo "🌐 Please open your browser and go to: $url"
    fi
}

if [ "$INSPECTOR_ONLY" = true ]; then
    # Launch inspector only (recommended approach)
    echo "🧪 Launching MCP Inspector with integrated server..."
    echo "💡 The inspector will start the server automatically"
    echo "💡 To stop, press Ctrl+C"
    echo ""

    # Launch inspector with our server
    npx @modelcontextprotocol/inspector node dist/index.js

elif [ "$LAUNCH_INSPECTOR" = true ]; then
    # Start server in background and launch inspector
    echo "✅ Starting MCP Shell Executor Server in background..."

    # Start server in background
    npm start &
    SERVER_PID=$!

    # Wait a moment for server to start
    sleep 2

    echo "🧪 Launching MCP Inspector..."

    # Launch inspector
    npx @modelcontextprotocol/inspector node dist/index.js &
    INSPECTOR_PID=$!

    # Cleanup function
    cleanup() {
        echo ""
        echo "🛑 Shutting down..."
        if [ ! -z "$SERVER_PID" ]; then
            kill $SERVER_PID 2>/dev/null
        fi
        if [ ! -z "$INSPECTOR_PID" ]; then
            kill $INSPECTOR_PID 2>/dev/null
        fi
        exit 0
    }

    # Set up signal handlers
    trap cleanup SIGINT SIGTERM

    echo "💡 Press Ctrl+C to stop both server and inspector"

    # Wait for processes
    wait

else
    # Default: Launch inspector with integrated server (recommended)
    echo "🧪 Launching MCP Inspector with Shell Executor..."
    echo "💡 Press Ctrl+C to stop"
    echo ""

    # Launch inspector with our server (inspector manages the server lifecycle)
    npx @modelcontextprotocol/inspector node dist/index.js
fi
